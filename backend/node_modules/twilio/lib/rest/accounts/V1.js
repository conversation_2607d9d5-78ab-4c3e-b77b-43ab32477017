"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Accounts
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const authTokenPromotion_1 = require("./v1/authTokenPromotion");
const bulkConsents_1 = require("./v1/bulkConsents");
const bulkContacts_1 = require("./v1/bulkContacts");
const credential_1 = require("./v1/credential");
const safelist_1 = require("./v1/safelist");
const secondaryAuthToken_1 = require("./v1/secondaryAuthToken");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Accounts
     *
     * @param domain - The Twilio (Twilio.Accounts) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for authTokenPromotion resource */
    get authTokenPromotion() {
        this._authTokenPromotion =
            this._authTokenPromotion || (0, authTokenPromotion_1.AuthTokenPromotionListInstance)(this);
        return this._authTokenPromotion;
    }
    /** Getter for bulkConsents resource */
    get bulkConsents() {
        this._bulkConsents = this._bulkConsents || (0, bulkConsents_1.BulkConsentsListInstance)(this);
        return this._bulkConsents;
    }
    /** Getter for bulkContacts resource */
    get bulkContacts() {
        this._bulkContacts = this._bulkContacts || (0, bulkContacts_1.BulkContactsListInstance)(this);
        return this._bulkContacts;
    }
    /** Getter for credentials resource */
    get credentials() {
        this._credentials = this._credentials || (0, credential_1.CredentialListInstance)(this);
        return this._credentials;
    }
    /** Getter for safelist resource */
    get safelist() {
        this._safelist = this._safelist || (0, safelist_1.SafelistListInstance)(this);
        return this._safelist;
    }
    /** Getter for secondaryAuthToken resource */
    get secondaryAuthToken() {
        this._secondaryAuthToken =
            this._secondaryAuthToken || (0, secondaryAuthToken_1.SecondaryAuthTokenListInstance)(this);
        return this._secondaryAuthToken;
    }
}
exports.default = V1;
