"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Accounts
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthTokenPromotionListInstance = exports.AuthTokenPromotionInstance = exports.AuthTokenPromotionContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
class AuthTokenPromotionContextImpl {
    constructor(_version) {
        this._version = _version;
        this._solution = {};
        this._uri = `/AuthTokens/Promote`;
    }
    update(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AuthTokenPromotionInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthTokenPromotionContextImpl = AuthTokenPromotionContextImpl;
class AuthTokenPromotionInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.authToken = payload.auth_token;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this._solution = {};
    }
    get _proxy() {
        this._context =
            this._context || new AuthTokenPromotionContextImpl(this._version);
        return this._context;
    }
    /**
     * Update a AuthTokenPromotionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AuthTokenPromotionInstance
     */
    update(callback) {
        return this._proxy.update(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            authToken: this.authToken,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthTokenPromotionInstance = AuthTokenPromotionInstance;
function AuthTokenPromotionListInstance(version) {
    const instance = (() => instance.get());
    instance.get = function get() {
        return new AuthTokenPromotionContextImpl(version);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AuthTokenPromotionListInstance = AuthTokenPromotionListInstance;
