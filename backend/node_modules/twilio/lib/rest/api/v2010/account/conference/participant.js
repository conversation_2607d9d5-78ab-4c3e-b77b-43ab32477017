"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantPage = exports.ParticipantListInstance = exports.ParticipantInstance = exports.ParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class ParticipantContextImpl {
    constructor(_version, accountSid, conferenceSid, callSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(conferenceSid)) {
            throw new Error("Parameter 'conferenceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(callSid)) {
            throw new Error("Parameter 'callSid' is not valid.");
        }
        this._solution = { accountSid, conferenceSid, callSid };
        this._uri = `/Accounts/${accountSid}/Conferences/${conferenceSid}/Participants/${callSid}.json`;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.conferenceSid, instance._solution.callSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["muted"] !== undefined)
            data["Muted"] = serialize.bool(params["muted"]);
        if (params["hold"] !== undefined)
            data["Hold"] = serialize.bool(params["hold"]);
        if (params["holdUrl"] !== undefined)
            data["HoldUrl"] = params["holdUrl"];
        if (params["holdMethod"] !== undefined)
            data["HoldMethod"] = params["holdMethod"];
        if (params["announceUrl"] !== undefined)
            data["AnnounceUrl"] = params["announceUrl"];
        if (params["announceMethod"] !== undefined)
            data["AnnounceMethod"] = params["announceMethod"];
        if (params["waitUrl"] !== undefined)
            data["WaitUrl"] = params["waitUrl"];
        if (params["waitMethod"] !== undefined)
            data["WaitMethod"] = params["waitMethod"];
        if (params["beepOnExit"] !== undefined)
            data["BeepOnExit"] = serialize.bool(params["beepOnExit"]);
        if (params["endConferenceOnExit"] !== undefined)
            data["EndConferenceOnExit"] = serialize.bool(params["endConferenceOnExit"]);
        if (params["coaching"] !== undefined)
            data["Coaching"] = serialize.bool(params["coaching"]);
        if (params["callSidToCoach"] !== undefined)
            data["CallSidToCoach"] = params["callSidToCoach"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.conferenceSid, instance._solution.callSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantContextImpl = ParticipantContextImpl;
class ParticipantInstance {
    constructor(_version, payload, accountSid, conferenceSid, callSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.callSid = payload.call_sid;
        this.label = payload.label;
        this.callSidToCoach = payload.call_sid_to_coach;
        this.coaching = payload.coaching;
        this.conferenceSid = payload.conference_sid;
        this.dateCreated = deserialize.rfc2822DateTime(payload.date_created);
        this.dateUpdated = deserialize.rfc2822DateTime(payload.date_updated);
        this.endConferenceOnExit = payload.end_conference_on_exit;
        this.muted = payload.muted;
        this.hold = payload.hold;
        this.startConferenceOnEnter = payload.start_conference_on_enter;
        this.status = payload.status;
        this.queueTime = payload.queue_time;
        this.uri = payload.uri;
        this._solution = {
            accountSid,
            conferenceSid,
            callSid: callSid || this.callSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ParticipantContextImpl(this._version, this._solution.accountSid, this._solution.conferenceSid, this._solution.callSid);
        return this._context;
    }
    /**
     * Remove a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ParticipantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            callSid: this.callSid,
            label: this.label,
            callSidToCoach: this.callSidToCoach,
            coaching: this.coaching,
            conferenceSid: this.conferenceSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            endConferenceOnExit: this.endConferenceOnExit,
            muted: this.muted,
            hold: this.hold,
            startConferenceOnEnter: this.startConferenceOnEnter,
            status: this.status,
            queueTime: this.queueTime,
            uri: this.uri,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantInstance = ParticipantInstance;
function ParticipantListInstance(version, accountSid, conferenceSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(conferenceSid)) {
        throw new Error("Parameter 'conferenceSid' is not valid.");
    }
    const instance = ((callSid) => instance.get(callSid));
    instance.get = function get(callSid) {
        return new ParticipantContextImpl(version, accountSid, conferenceSid, callSid);
    };
    instance._version = version;
    instance._solution = { accountSid, conferenceSid };
    instance._uri = `/Accounts/${accountSid}/Conferences/${conferenceSid}/Participants.json`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["from"] === null || params["from"] === undefined) {
            throw new Error("Required parameter \"params['from']\" missing.");
        }
        if (params["to"] === null || params["to"] === undefined) {
            throw new Error("Required parameter \"params['to']\" missing.");
        }
        let data = {};
        data["From"] = params["from"];
        data["To"] = params["to"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["statusCallbackEvent"] !== undefined)
            data["StatusCallbackEvent"] = serialize.map(params["statusCallbackEvent"], (e) => e);
        if (params["label"] !== undefined)
            data["Label"] = params["label"];
        if (params["timeout"] !== undefined)
            data["Timeout"] = params["timeout"];
        if (params["record"] !== undefined)
            data["Record"] = serialize.bool(params["record"]);
        if (params["muted"] !== undefined)
            data["Muted"] = serialize.bool(params["muted"]);
        if (params["beep"] !== undefined)
            data["Beep"] = params["beep"];
        if (params["startConferenceOnEnter"] !== undefined)
            data["StartConferenceOnEnter"] = serialize.bool(params["startConferenceOnEnter"]);
        if (params["endConferenceOnExit"] !== undefined)
            data["EndConferenceOnExit"] = serialize.bool(params["endConferenceOnExit"]);
        if (params["waitUrl"] !== undefined)
            data["WaitUrl"] = params["waitUrl"];
        if (params["waitMethod"] !== undefined)
            data["WaitMethod"] = params["waitMethod"];
        if (params["earlyMedia"] !== undefined)
            data["EarlyMedia"] = serialize.bool(params["earlyMedia"]);
        if (params["maxParticipants"] !== undefined)
            data["MaxParticipants"] = params["maxParticipants"];
        if (params["conferenceRecord"] !== undefined)
            data["ConferenceRecord"] = params["conferenceRecord"];
        if (params["conferenceTrim"] !== undefined)
            data["ConferenceTrim"] = params["conferenceTrim"];
        if (params["conferenceStatusCallback"] !== undefined)
            data["ConferenceStatusCallback"] = params["conferenceStatusCallback"];
        if (params["conferenceStatusCallbackMethod"] !== undefined)
            data["ConferenceStatusCallbackMethod"] =
                params["conferenceStatusCallbackMethod"];
        if (params["conferenceStatusCallbackEvent"] !== undefined)
            data["ConferenceStatusCallbackEvent"] = serialize.map(params["conferenceStatusCallbackEvent"], (e) => e);
        if (params["recordingChannels"] !== undefined)
            data["RecordingChannels"] = params["recordingChannels"];
        if (params["recordingStatusCallback"] !== undefined)
            data["RecordingStatusCallback"] = params["recordingStatusCallback"];
        if (params["recordingStatusCallbackMethod"] !== undefined)
            data["RecordingStatusCallbackMethod"] =
                params["recordingStatusCallbackMethod"];
        if (params["sipAuthUsername"] !== undefined)
            data["SipAuthUsername"] = params["sipAuthUsername"];
        if (params["sipAuthPassword"] !== undefined)
            data["SipAuthPassword"] = params["sipAuthPassword"];
        if (params["region"] !== undefined)
            data["Region"] = params["region"];
        if (params["conferenceRecordingStatusCallback"] !== undefined)
            data["ConferenceRecordingStatusCallback"] =
                params["conferenceRecordingStatusCallback"];
        if (params["conferenceRecordingStatusCallbackMethod"] !== undefined)
            data["ConferenceRecordingStatusCallbackMethod"] =
                params["conferenceRecordingStatusCallbackMethod"];
        if (params["recordingStatusCallbackEvent"] !== undefined)
            data["RecordingStatusCallbackEvent"] = serialize.map(params["recordingStatusCallbackEvent"], (e) => e);
        if (params["conferenceRecordingStatusCallbackEvent"] !== undefined)
            data["ConferenceRecordingStatusCallbackEvent"] = serialize.map(params["conferenceRecordingStatusCallbackEvent"], (e) => e);
        if (params["coaching"] !== undefined)
            data["Coaching"] = serialize.bool(params["coaching"]);
        if (params["callSidToCoach"] !== undefined)
            data["CallSidToCoach"] = params["callSidToCoach"];
        if (params["jitterBufferSize"] !== undefined)
            data["JitterBufferSize"] = params["jitterBufferSize"];
        if (params["byoc"] !== undefined)
            data["Byoc"] = params["byoc"];
        if (params["callerId"] !== undefined)
            data["CallerId"] = params["callerId"];
        if (params["callReason"] !== undefined)
            data["CallReason"] = params["callReason"];
        if (params["recordingTrack"] !== undefined)
            data["RecordingTrack"] = params["recordingTrack"];
        if (params["timeLimit"] !== undefined)
            data["TimeLimit"] = params["timeLimit"];
        if (params["machineDetection"] !== undefined)
            data["MachineDetection"] = params["machineDetection"];
        if (params["machineDetectionTimeout"] !== undefined)
            data["MachineDetectionTimeout"] = params["machineDetectionTimeout"];
        if (params["machineDetectionSpeechThreshold"] !== undefined)
            data["MachineDetectionSpeechThreshold"] =
                params["machineDetectionSpeechThreshold"];
        if (params["machineDetectionSpeechEndThreshold"] !== undefined)
            data["MachineDetectionSpeechEndThreshold"] =
                params["machineDetectionSpeechEndThreshold"];
        if (params["machineDetectionSilenceTimeout"] !== undefined)
            data["MachineDetectionSilenceTimeout"] =
                params["machineDetectionSilenceTimeout"];
        if (params["amdStatusCallback"] !== undefined)
            data["AmdStatusCallback"] = params["amdStatusCallback"];
        if (params["amdStatusCallbackMethod"] !== undefined)
            data["AmdStatusCallbackMethod"] = params["amdStatusCallbackMethod"];
        if (params["trim"] !== undefined)
            data["Trim"] = params["trim"];
        if (params["callToken"] !== undefined)
            data["CallToken"] = params["callToken"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.conferenceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["muted"] !== undefined)
            data["Muted"] = serialize.bool(params["muted"]);
        if (params["hold"] !== undefined)
            data["Hold"] = serialize.bool(params["hold"]);
        if (params["coaching"] !== undefined)
            data["Coaching"] = serialize.bool(params["coaching"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantListInstance = ParticipantListInstance;
class ParticipantPage extends Page_1.default {
    /**
     * Initialize the ParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantInstance(this._version, payload, this._solution.accountSid, this._solution.conferenceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantPage = ParticipantPage;
