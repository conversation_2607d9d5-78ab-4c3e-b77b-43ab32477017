"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranscriptionListInstance = exports.TranscriptionInstance = exports.TranscriptionContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class TranscriptionContextImpl {
    constructor(_version, accountSid, callSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(callSid)) {
            throw new Error("Parameter 'callSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { accountSid, callSid, sid };
        this._uri = `/Accounts/${accountSid}/Calls/${callSid}/Transcriptions/${sid}.json`;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["status"] === null || params["status"] === undefined) {
            throw new Error("Required parameter \"params['status']\" missing.");
        }
        let data = {};
        data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TranscriptionInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.callSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TranscriptionContextImpl = TranscriptionContextImpl;
class TranscriptionInstance {
    constructor(_version, payload, accountSid, callSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.callSid = payload.call_sid;
        this.name = payload.name;
        this.status = payload.status;
        this.dateUpdated = deserialize.rfc2822DateTime(payload.date_updated);
        this.uri = payload.uri;
        this._solution = { accountSid, callSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new TranscriptionContextImpl(this._version, this._solution.accountSid, this._solution.callSid, this._solution.sid);
        return this._context;
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            callSid: this.callSid,
            name: this.name,
            status: this.status,
            dateUpdated: this.dateUpdated,
            uri: this.uri,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TranscriptionInstance = TranscriptionInstance;
function TranscriptionListInstance(version, accountSid, callSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(callSid)) {
        throw new Error("Parameter 'callSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new TranscriptionContextImpl(version, accountSid, callSid, sid);
    };
    instance._version = version;
    instance._solution = { accountSid, callSid };
    instance._uri = `/Accounts/${accountSid}/Calls/${callSid}/Transcriptions.json`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["name"] !== undefined)
            data["Name"] = params["name"];
        if (params["track"] !== undefined)
            data["Track"] = params["track"];
        if (params["statusCallbackUrl"] !== undefined)
            data["StatusCallbackUrl"] = params["statusCallbackUrl"];
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["inboundTrackLabel"] !== undefined)
            data["InboundTrackLabel"] = params["inboundTrackLabel"];
        if (params["outboundTrackLabel"] !== undefined)
            data["OutboundTrackLabel"] = params["outboundTrackLabel"];
        if (params["partialResults"] !== undefined)
            data["PartialResults"] = serialize.bool(params["partialResults"]);
        if (params["languageCode"] !== undefined)
            data["LanguageCode"] = params["languageCode"];
        if (params["transcriptionEngine"] !== undefined)
            data["TranscriptionEngine"] = params["transcriptionEngine"];
        if (params["profanityFilter"] !== undefined)
            data["ProfanityFilter"] = serialize.bool(params["profanityFilter"]);
        if (params["speechModel"] !== undefined)
            data["SpeechModel"] = params["speechModel"];
        if (params["hints"] !== undefined)
            data["Hints"] = params["hints"];
        if (params["enableAutomaticPunctuation"] !== undefined)
            data["EnableAutomaticPunctuation"] = serialize.bool(params["enableAutomaticPunctuation"]);
        if (params["intelligenceService"] !== undefined)
            data["IntelligenceService"] = params["intelligenceService"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TranscriptionInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.callSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.TranscriptionListInstance = TranscriptionListInstance;
