"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignedAddOnExtensionPage = exports.AssignedAddOnExtensionListInstance = exports.AssignedAddOnExtensionInstance = exports.AssignedAddOnExtensionContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../../base/Page"));
const deserialize = require("../../../../../../base/deserialize");
const serialize = require("../../../../../../base/serialize");
const utility_1 = require("../../../../../../base/utility");
class AssignedAddOnExtensionContextImpl {
    constructor(_version, accountSid, resourceSid, assignedAddOnSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(resourceSid)) {
            throw new Error("Parameter 'resourceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(assignedAddOnSid)) {
            throw new Error("Parameter 'assignedAddOnSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { accountSid, resourceSid, assignedAddOnSid, sid };
        this._uri = `/Accounts/${accountSid}/IncomingPhoneNumbers/${resourceSid}/AssignedAddOns/${assignedAddOnSid}/Extensions/${sid}.json`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssignedAddOnExtensionInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.resourceSid, instance._solution.assignedAddOnSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnExtensionContextImpl = AssignedAddOnExtensionContextImpl;
class AssignedAddOnExtensionInstance {
    constructor(_version, payload, accountSid, resourceSid, assignedAddOnSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.resourceSid = payload.resource_sid;
        this.assignedAddOnSid = payload.assigned_add_on_sid;
        this.friendlyName = payload.friendly_name;
        this.productName = payload.product_name;
        this.uniqueName = payload.unique_name;
        this.uri = payload.uri;
        this.enabled = payload.enabled;
        this._solution = {
            accountSid,
            resourceSid,
            assignedAddOnSid,
            sid: sid || this.sid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AssignedAddOnExtensionContextImpl(this._version, this._solution.accountSid, this._solution.resourceSid, this._solution.assignedAddOnSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a AssignedAddOnExtensionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AssignedAddOnExtensionInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            resourceSid: this.resourceSid,
            assignedAddOnSid: this.assignedAddOnSid,
            friendlyName: this.friendlyName,
            productName: this.productName,
            uniqueName: this.uniqueName,
            uri: this.uri,
            enabled: this.enabled,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnExtensionInstance = AssignedAddOnExtensionInstance;
function AssignedAddOnExtensionListInstance(version, accountSid, resourceSid, assignedAddOnSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(resourceSid)) {
        throw new Error("Parameter 'resourceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(assignedAddOnSid)) {
        throw new Error("Parameter 'assignedAddOnSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new AssignedAddOnExtensionContextImpl(version, accountSid, resourceSid, assignedAddOnSid, sid);
    };
    instance._version = version;
    instance._solution = { accountSid, resourceSid, assignedAddOnSid };
    instance._uri = `/Accounts/${accountSid}/IncomingPhoneNumbers/${resourceSid}/AssignedAddOns/${assignedAddOnSid}/Extensions.json`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssignedAddOnExtensionPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AssignedAddOnExtensionPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AssignedAddOnExtensionListInstance = AssignedAddOnExtensionListInstance;
class AssignedAddOnExtensionPage extends Page_1.default {
    /**
     * Initialize the AssignedAddOnExtensionPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AssignedAddOnExtensionInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AssignedAddOnExtensionInstance(this._version, payload, this._solution.accountSid, this._solution.resourceSid, this._solution.assignedAddOnSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnExtensionPage = AssignedAddOnExtensionPage;
