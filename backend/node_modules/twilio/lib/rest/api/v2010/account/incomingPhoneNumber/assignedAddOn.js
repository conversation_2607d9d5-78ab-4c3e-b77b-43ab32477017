"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignedAddOnPage = exports.AssignedAddOnListInstance = exports.AssignedAddOnInstance = exports.AssignedAddOnContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
const assignedAddOnExtension_1 = require("./assignedAddOn/assignedAddOnExtension");
class AssignedAddOnContextImpl {
    constructor(_version, accountSid, resourceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(resourceSid)) {
            throw new Error("Parameter 'resourceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { accountSid, resourceSid, sid };
        this._uri = `/Accounts/${accountSid}/IncomingPhoneNumbers/${resourceSid}/AssignedAddOns/${sid}.json`;
    }
    get extensions() {
        this._extensions =
            this._extensions ||
                (0, assignedAddOnExtension_1.AssignedAddOnExtensionListInstance)(this._version, this._solution.accountSid, this._solution.resourceSid, this._solution.sid);
        return this._extensions;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssignedAddOnInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.resourceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnContextImpl = AssignedAddOnContextImpl;
class AssignedAddOnInstance {
    constructor(_version, payload, accountSid, resourceSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.resourceSid = payload.resource_sid;
        this.friendlyName = payload.friendly_name;
        this.description = payload.description;
        this.configuration = payload.configuration;
        this.uniqueName = payload.unique_name;
        this.dateCreated = deserialize.rfc2822DateTime(payload.date_created);
        this.dateUpdated = deserialize.rfc2822DateTime(payload.date_updated);
        this.uri = payload.uri;
        this.subresourceUris = payload.subresource_uris;
        this._solution = { accountSid, resourceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AssignedAddOnContextImpl(this._version, this._solution.accountSid, this._solution.resourceSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a AssignedAddOnInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a AssignedAddOnInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AssignedAddOnInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the extensions.
     */
    extensions() {
        return this._proxy.extensions;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            resourceSid: this.resourceSid,
            friendlyName: this.friendlyName,
            description: this.description,
            configuration: this.configuration,
            uniqueName: this.uniqueName,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            uri: this.uri,
            subresourceUris: this.subresourceUris,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnInstance = AssignedAddOnInstance;
function AssignedAddOnListInstance(version, accountSid, resourceSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(resourceSid)) {
        throw new Error("Parameter 'resourceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new AssignedAddOnContextImpl(version, accountSid, resourceSid, sid);
    };
    instance._version = version;
    instance._solution = { accountSid, resourceSid };
    instance._uri = `/Accounts/${accountSid}/IncomingPhoneNumbers/${resourceSid}/AssignedAddOns.json`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["installedAddOnSid"] === null ||
            params["installedAddOnSid"] === undefined) {
            throw new Error("Required parameter \"params['installedAddOnSid']\" missing.");
        }
        let data = {};
        data["InstalledAddOnSid"] = params["installedAddOnSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssignedAddOnInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.resourceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssignedAddOnPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AssignedAddOnPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AssignedAddOnListInstance = AssignedAddOnListInstance;
class AssignedAddOnPage extends Page_1.default {
    /**
     * Initialize the AssignedAddOnPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AssignedAddOnInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AssignedAddOnInstance(this._version, payload, this._solution.accountSid, this._solution.resourceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssignedAddOnPage = AssignedAddOnPage;
