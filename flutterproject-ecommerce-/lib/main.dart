import 'package:ecom/features/admin/bottom_nav/bottom_nav.dart';
import 'package:ecom/features/admin/home/<USER>/admin_dashboard.dart';
import 'package:ecom/features/auth/provider/forgetpassword.dart';
import 'package:ecom/features/auth/provider/login.dart';
import 'package:ecom/features/auth/provider/otpprovider.dart';
import 'package:ecom/features/auth/provider/registerprovider.dart';
import 'package:ecom/features/auth/provider/resetprovider.dart';
import 'package:ecom/features/cutomer/cart/provider/cart_provider.dart';
import 'package:ecom/features/cutomer/orders/provider/order_provider.dart';
import 'package:ecom/features/cutomer/products/provider/featuresproductprovider.dart';
import 'package:ecom/features/cutomer/products/provider/getallproductsprovider.dart';
import 'package:ecom/features/cutomer/products/provider/getproductbyidprovider.dart';
import 'package:ecom/features/cutomer/profile/provider/update_user_profile_img.dart';
import 'package:ecom/features/on_boarding/on_boarding_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// import 'features/cutomer/bottom_nav/bottom_nav.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => RegisterProvider()),
        ChangeNotifierProvider(create: (_) => LoginProvider()),
        ChangeNotifierProvider(create: (_) => ForgetPasswordProvider()),
        ChangeNotifierProvider(create: (_) => OtpProvider()),
        ChangeNotifierProvider(create: (_) => ResetPasswordProvider()),
        ChangeNotifierProvider(create: (_) => GetAllProductProvider()),
        ChangeNotifierProvider(create: (_) => GetProductProductsByIdProvider()),
        ChangeNotifierProvider(create: (_) => FeaturesProductProvider()),
        ChangeNotifierProvider(create: (_) => UpdateUserProfileImgProvider()),
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
      ],

      child: MaterialApp(
        home: Builder(
          builder: (context) {
            return OnBoardingScreen();
          },
        ),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
