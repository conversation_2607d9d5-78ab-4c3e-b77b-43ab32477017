import 'package:ecom/core/services/auth_service.dart';
import 'package:ecom/features/auth/provider/forgetpassword.dart';
import 'package:ecom/features/auth/provider/login.dart';
import 'package:ecom/features/auth/provider/otpprovider.dart';
import 'package:ecom/features/auth/provider/registerprovider.dart';
import 'package:ecom/features/auth/provider/resetprovider.dart';
import 'package:ecom/features/cutomer/cart/provider/cart_provider.dart';
import 'package:ecom/features/cutomer/categories/provider/category_provider.dart';
import 'package:ecom/features/cutomer/orders/provider/order_provider.dart';
import 'package:ecom/features/cutomer/products/provider/featuresproductprovider.dart';
import 'package:ecom/features/cutomer/products/provider/getallproductsprovider.dart';
import 'package:ecom/features/cutomer/products/provider/getproductbyidprovider.dart';
import 'package:ecom/features/cutomer/profile/provider/update_user_profile_img.dart';
import 'package:ecom/features/cutomer/profile/provider/user_profile_provider.dart';
import 'package:ecom/features/cutomer/search/provider/search_provider.dart';
import 'package:ecom/features/cutomer/payment/provider/payment_provider.dart';
import 'package:ecom/features/on_boarding/on_boarding_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// import 'features/cutomer/bottom_nav/bottom_nav.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize AuthService
  await AuthService.instance.init();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => RegisterProvider()),
        ChangeNotifierProvider(
          create: (_) {
            final loginProvider = LoginProvider();
            // Initialize authentication state
            WidgetsBinding.instance.addPostFrameCallback((_) {
              loginProvider.initializeAuth();
            });
            return loginProvider;
          },
        ),
        ChangeNotifierProvider(create: (_) => ForgetPasswordProvider()),
        ChangeNotifierProvider(create: (_) => OtpProvider()),
        ChangeNotifierProvider(create: (_) => ResetPasswordProvider()),
        ChangeNotifierProvider(create: (_) => GetAllProductProvider()),
        ChangeNotifierProvider(create: (_) => GetProductProductsByIdProvider()),
        ChangeNotifierProvider(create: (_) => FeaturesProductProvider()),
        ChangeNotifierProvider(create: (_) => UpdateUserProfileImgProvider()),
        ChangeNotifierProvider(create: (_) => UserProfileProvider()),
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProvider(create: (_) => PaymentProvider()),
      ],
      child: MaterialApp(
        home: Consumer<LoginProvider>(
          builder: (context, loginProvider, child) {
            // Show loading screen while initializing authentication
            if (loginProvider.isInitializing) {
              return const Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Colors.green),
                      SizedBox(height: 16),
                      Text('Initializing...'),
                    ],
                  ),
                ),
              );
            }

            // For now, always show OnBoardingScreen
            // In a complete implementation, you might check loginProvider.isLoggedIn
            // and navigate to the main app or login screen accordingly
            return OnBoardingScreen();
          },
        ),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
