import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../cutomer/categories/provider/category_provider.dart';

class CategoryPage extends StatefulWidget {
  const CategoryPage({super.key});

  @override
  State<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> {
  // Default category icons mapping
  final Map<String, IconData> categoryIcons = {
    'vegetables': Icons.local_florist,
    'fruits': Icons.apple,
    'beverages': Icons.local_drink,
    'grocery': Icons.shopping_basket,
    'frozen': Icons.ac_unit,
    'desserts': Icons.cake,
    'electronics': Icons.devices,
    'clothing': Icons.checkroom,
    'books': Icons.book,
    'sports': Icons.sports_soccer,
    'home': Icons.home,
    'beauty': Icons.face,
  };

  // Default category colors
  final List<Color> categoryColors = [
    Color(0xFF4CAF50),
    Color(0xFFFF5722),
    Color(0xFFFFC107),
    Color(0xFF9C27B0),
    Color(0xFF00BCD4),
    Color(0xFFE91E63),
    Color(0xFF2196F3),
    Color(0xFFFF9800),
    Color(0xFF795548),
    Color(0xFF607D8B),
    Color(0xFF9E9E9E),
    Color(0xFFF44336),
  ];

  @override
  void initState() {
    super.initState();
    // Load categories when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CategoryProvider>().getAllCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Categories',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _navigateToAllCategories(context),
                    child: Row(
                      children: [
                        Text(
                          'See All',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.green[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(Icons.arrow_forward_ios,
                            size: 16, color: Colors.green[600]),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            SizedBox(
              height: 100,
              child: _buildCategoryList(categoryProvider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryList(CategoryProvider categoryProvider) {
    if (categoryProvider.isLoading) {
      return Center(
        child: CircularProgressIndicator(color: Colors.green[600]),
      );
    }

    if (categoryProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red[400], size: 32),
            SizedBox(height: 8),
            Text(
              'Failed to load categories',
              style: TextStyle(color: Colors.red[600], fontSize: 12),
            ),
            SizedBox(height: 4),
            GestureDetector(
              onTap: () => categoryProvider.getAllCategories(),
              child: Text(
                'Tap to retry',
                style: TextStyle(
                  color: Colors.green[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (categoryProvider.categories.isEmpty) {
      return Center(
        child: Text(
          'No categories available',
          style: TextStyle(color: Colors.grey[600], fontSize: 14),
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: categoryProvider.categories.length,
      itemBuilder: (context, index) {
        final category = categoryProvider.categories[index];
        final color = categoryColors[index % categoryColors.length];
        final icon = _getCategoryIcon(category.name);

        return GestureDetector(
          onTap: () => _onCategoryTap(context, category),
          child: Container(
            width: 70,
            margin: const EdgeInsets.only(right: 15),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  category.name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getCategoryIcon(String categoryName) {
    final key = categoryName.toLowerCase();
    return categoryIcons[key] ?? Icons.category;
  }

  void _onCategoryTap(BuildContext context, category) {
    // TODO: Navigate to category products page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected category: ${category.name}'),
        backgroundColor: Colors.green[600],
      ),
    );
  }

  void _navigateToAllCategories(BuildContext context) {
    // TODO: Navigate to all categories page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to all categories'),
        backgroundColor: Colors.green[600],
      ),
    );
  }
}
