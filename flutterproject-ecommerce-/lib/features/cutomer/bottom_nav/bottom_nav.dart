import 'package:ecom/features/cutomer/cart/pages/cart_add.dart';
import 'package:ecom/features/cutomer/category/pages/category.dart';
import 'package:ecom/features/cutomer/profile/pages/profileimage.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../cart/provider/cart_provider.dart';

import '../home/<USER>';

class ButtonNavPage extends StatefulWidget {
  const ButtonNavPage({super.key});

  @override
  State<ButtonNavPage> createState() => _ButtonNavPageState();
}

class _ButtonNavPageState extends State<ButtonNavPage> {
  int currentIndex = 0;
  List screen = [HomePages(), CartPage(), CategoryPage(), ProfilePage()];

  @override
  void initState() {
    super.initState();
    // Load cart when bottom nav initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CartProvider>().getCart();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: screen[currentIndex],
      bottomNavigationBar: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          return BottomNavigationBar(
            currentIndex: currentIndex,
            onTap: (value) => setState(() {
              currentIndex = value;
            }),
            selectedItemColor: Colors.green,
            unselectedItemColor: const Color.fromARGB(255, 102, 124, 109),
            type: BottomNavigationBarType.fixed,
            items: [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: "Home"),
              BottomNavigationBarItem(
                icon: _buildCartIcon(cartProvider.totalItems),
                label: "Cart",
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.category),
                label: "Category",
              ),
              BottomNavigationBarItem(
                  icon: Icon(Icons.person), label: "Profile"),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCartIcon(int itemCount) {
    return Stack(
      children: [
        Icon(Icons.shopping_cart),
        if (itemCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                itemCount > 99 ? '99+' : '$itemCount',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
