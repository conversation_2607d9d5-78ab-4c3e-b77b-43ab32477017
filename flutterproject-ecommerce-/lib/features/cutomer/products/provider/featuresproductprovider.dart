import 'dart:developer';

import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/cutomer/products/model/productmodel.dart';
import 'package:flutter/widgets.dart';

class FeaturesProductProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;

  List<ProductModel>? _productModel = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ProductModel>? get productModel => _productModel;

  FeaturesProductProvider() {
    featureProduct();
  }

  Future<void> featureProduct() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/products/all/featured");

      final data = response.data['data'] as List;
      _productModel = data.map((e) => ProductModel.fromJson(e)).toList();
      log(_productModel.toString());
    } catch (e) {
      _error = e.toString();
      log(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
