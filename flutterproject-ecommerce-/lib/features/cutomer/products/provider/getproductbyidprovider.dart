import 'dart:developer';

import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/cutomer/products/model/productmodel.dart';
import 'package:flutter/widgets.dart';

class GetProductProductsByIdProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  ProductModel? _productModel;

  bool get isLoading => _isLoading;
  String? get error => _error;
  ProductModel? get userModel => _productModel;

  Future<void> getProductById({
    required String createdBy,
    required String id,
    required String name,
    required final String brand,
    required final String category,
    required final int price,
    required final int stock,
    required final List<String> imageUrls,
    required final bool featured,
    required final String description,
    required final DateTime createdAt,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get(
        "/products/$id",
      );

      _productModel = ProductModel.fromJson(response.data);
      log(_productModel.toString());
    } catch (e) {
      _error = e.toString();
      log(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
