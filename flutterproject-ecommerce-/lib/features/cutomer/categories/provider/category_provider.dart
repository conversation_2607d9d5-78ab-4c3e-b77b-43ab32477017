import 'dart:developer';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../model/category_model.dart';

class CategoryProvider with ChangeNotifier {
  final DioClient dioClient = DioClient();
  
  bool _isLoading = false;
  String? _error;
  List<CategoryModel> _categories = [];
  CategoryModel? _selectedCategory;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<CategoryModel> get categories => _categories;
  CategoryModel? get selectedCategory => _selectedCategory;

  // Get all categories
  Future<void> getAllCategories() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/category");
      
      if (response.data != null) {
        _categories = (response.data as List)
            .map((categoryJson) => CategoryModel.fromJson(categoryJson))
            .toList();
        log("Categories loaded: ${_categories.length} categories");
      }
    } catch (e) {
      _error = "Failed to load categories: $e";
      log("Error loading categories: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get category by ID
  Future<void> getCategoryById(String categoryId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/category/$categoryId");
      
      if (response.data != null) {
        _selectedCategory = CategoryModel.fromJson(response.data);
        log("Category loaded: ${_selectedCategory?.name}");
      }
    } catch (e) {
      _error = "Failed to load category: $e";
      log("Error loading category: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create new category (Admin only)
  Future<bool> createCategory({
    required String name,
    String? description,
    String? imageUrl,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/category",
        data: {
          'name': name,
          'description': description,
          'imageUrl': imageUrl,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        log("Category created successfully: $name");
        
        // Refresh categories list
        await getAllCategories();
        return true;
      } else {
        _error = "Failed to create category";
        return false;
      }
    } catch (e) {
      _error = "Failed to create category: $e";
      log("Error creating category: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update category (Admin only)
  Future<bool> updateCategory({
    required String categoryId,
    required String name,
    String? description,
    String? imageUrl,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/category/$categoryId",
        data: {
          'name': name,
          'description': description,
          'imageUrl': imageUrl,
        },
      );

      if (response.statusCode == 200) {
        log("Category updated successfully: $name");
        
        // Refresh categories list
        await getAllCategories();
        return true;
      } else {
        _error = "Failed to update category";
        return false;
      }
    } catch (e) {
      _error = "Failed to update category: $e";
      log("Error updating category: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete category (Admin only)
  Future<bool> deleteCategory(String categoryId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.delete("/category/$categoryId");

      if (response.statusCode == 200) {
        log("Category deleted successfully");
        
        // Refresh categories list
        await getAllCategories();
        return true;
      } else {
        _error = "Failed to delete category";
        return false;
      }
    } catch (e) {
      _error = "Failed to delete category: $e";
      log("Error deleting category: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Set selected category
  void setSelectedCategory(CategoryModel? category) {
    _selectedCategory = category;
    notifyListeners();
  }

  // Get category by name
  CategoryModel? getCategoryByName(String name) {
    try {
      return _categories.firstWhere(
        (category) => category.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  // Search categories by name
  List<CategoryModel> searchCategories(String query) {
    if (query.isEmpty) return _categories;
    
    return _categories.where((category) =>
      category.name.toLowerCase().contains(query.toLowerCase()) ||
      (category.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
    ).toList();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear selected category
  void clearSelectedCategory() {
    _selectedCategory = null;
    notifyListeners();
  }

  // Initialize categories (call this when app starts)
  Future<void> initializeCategories() async {
    await getAllCategories();
  }

  // Check if category exists
  bool categoryExists(String name) {
    return _categories.any(
      (category) => category.name.toLowerCase() == name.toLowerCase(),
    );
  }

  // Get categories count
  int get categoriesCount => _categories.length;

  // Check if categories are empty
  bool get isEmpty => _categories.isEmpty;

  // Check if categories are not empty
  bool get isNotEmpty => _categories.isNotEmpty;
}
