import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../model/cart_model.dart';
import '../model/cart_items.dart';

class CartProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  bool _isAddingToCart = false;
  bool _isUpdatingCart = false;
  bool _isRemovingFromCart = false;
  String? _error;
  CartModel? _cart;

  // Getters
  bool get isLoading => _isLoading;
  bool get isAddingToCart => _isAddingToCart;
  bool get isUpdatingCart => _isUpdatingCart;
  bool get isRemovingFromCart => _isRemovingFromCart;
  String? get error => _error;
  CartModel? get cart => _cart;
  List<CartItems> get cartItems => _cart?.items ?? [];
  double get totalAmount => _cart?.totalAmount ?? 0.0;
  int get totalItems => _cart?.totalItems ?? 0;
  bool get isEmpty => _cart?.isEmpty ?? true;

  // Get user's cart
  Future<void> getCart() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/cart");

      if (response.data != null) {
        _cart = CartModel.fromJson(response.data);
        log("Cart loaded: ${_cart?.items.length} items");
      }
    } catch (e) {
      _error = "Failed to load cart: $e";
      log("Error loading cart: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add item to cart
  Future<bool> addToCart({
    required String productId,
    required int quantity,
  }) async {
    _isAddingToCart = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/cart/add",
        data: {'productId': productId, 'quantity': quantity},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Refresh cart after adding item
        await getCart();
        log("Item added to cart successfully");
        return true;
      } else {
        _error = "Failed to add item to cart";
        return false;
      }
    } catch (e) {
      _error = "Failed to add item to cart: $e";
      log("Error adding to cart: $_error");
      return false;
    } finally {
      _isAddingToCart = false;
      notifyListeners();
    }
  }

  // Update cart item quantity
  Future<bool> updateCartItem({
    required String productId,
    required int quantity,
  }) async {
    _isUpdatingCart = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/cart/update",
        data: {'productId': productId, 'quantity': quantity},
      );

      if (response.statusCode == 200) {
        // Refresh cart after updating
        await getCart();
        log("Cart item updated successfully");
        return true;
      } else {
        _error = "Failed to update cart item";
        return false;
      }
    } catch (e) {
      _error = "Failed to update cart item: $e";
      log("Error updating cart: $_error");
      return false;
    } finally {
      _isUpdatingCart = false;
      notifyListeners();
    }
  }

  // Remove item from cart
  Future<bool> removeFromCart({required String productId}) async {
    _isRemovingFromCart = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.delete(
        "/cart/remove",
        data: {'productId': productId},
      );

      if (response.statusCode == 200) {
        // Refresh cart after removing item
        await getCart();
        log("Item removed from cart successfully");
        return true;
      } else {
        _error = "Failed to remove item from cart";
        return false;
      }
    } catch (e) {
      _error = "Failed to remove item from cart: $e";
      log("Error removing from cart: $_error");
      return false;
    } finally {
      _isRemovingFromCart = false;
      notifyListeners();
    }
  }

  // Clear entire cart
  Future<bool> clearCart() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.delete("/cart/clear");

      if (response.statusCode == 200) {
        _cart = null;
        log("Cart cleared successfully");
        return true;
      } else {
        _error = "Failed to clear cart";
        return false;
      }
    } catch (e) {
      _error = "Failed to clear cart: $e";
      log("Error clearing cart: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get cart item by product ID
  CartItems? getCartItem(String productId) {
    if (_cart == null) return null;
    try {
      return _cart!.items.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Check if product is in cart
  bool isInCart(String productId) {
    return getCartItem(productId) != null;
  }

  // Get quantity of specific product in cart
  int getProductQuantity(String productId) {
    final cartItem = getCartItem(productId);
    return cartItem?.quantity ?? 0;
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Initialize cart (call this when user logs in)
  Future<void> initializeCart() async {
    await getCart();
  }
}
