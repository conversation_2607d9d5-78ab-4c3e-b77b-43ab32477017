import 'dart:developer';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../model/payment_model.dart';

class PaymentProvider with ChangeNotifier {
  final DioClient dioClient = DioClient();
  
  bool _isLoading = false;
  bool _isProcessingPayment = false;
  String? _error;
  List<PaymentMethod> _paymentMethods = [];
  PaymentMethod? _selectedPaymentMethod;
  PaymentResult? _lastPaymentResult;
  List<PaymentHistory> _paymentHistory = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get isProcessingPayment => _isProcessingPayment;
  String? get error => _error;
  List<PaymentMethod> get paymentMethods => _paymentMethods;
  PaymentMethod? get selectedPaymentMethod => _selectedPaymentMethod;
  PaymentResult? get lastPaymentResult => _lastPaymentResult;
  List<PaymentHistory> get paymentHistory => _paymentHistory;

  PaymentProvider() {
    _initializePaymentMethods();
  }

  // Initialize available payment methods
  void _initializePaymentMethods() {
    _paymentMethods = [
      PaymentMethod(
        id: 'khalti',
        name: 'Khalti',
        type: PaymentType.digitalWallet,
        icon: 'assets/images/khalti_logo.png',
        isEnabled: true,
        description: 'Pay with Khalti digital wallet',
      ),
      PaymentMethod(
        id: 'esewa',
        name: 'eSewa',
        type: PaymentType.digitalWallet,
        icon: 'assets/images/esewa_logo.png',
        isEnabled: true,
        description: 'Pay with eSewa digital wallet',
      ),
      PaymentMethod(
        id: 'card',
        name: 'Credit/Debit Card',
        type: PaymentType.card,
        icon: 'assets/images/card_icon.png',
        isEnabled: true,
        description: 'Pay with your credit or debit card',
      ),
      PaymentMethod(
        id: 'bank_transfer',
        name: 'Bank Transfer',
        type: PaymentType.bankTransfer,
        icon: 'assets/images/bank_icon.png',
        isEnabled: true,
        description: 'Direct bank transfer',
      ),
      PaymentMethod(
        id: 'cod',
        name: 'Cash on Delivery',
        type: PaymentType.cashOnDelivery,
        icon: 'assets/images/cod_icon.png',
        isEnabled: true,
        description: 'Pay when you receive your order',
      ),
    ];
    notifyListeners();
  }

  // Select payment method
  void selectPaymentMethod(PaymentMethod method) {
    _selectedPaymentMethod = method;
    notifyListeners();
  }

  // Process payment
  Future<bool> processPayment({
    required String orderId,
    required double amount,
    required Map<String, dynamic> customerInfo,
    Map<String, dynamic>? additionalData,
  }) async {
    if (_selectedPaymentMethod == null) {
      _error = "Please select a payment method";
      notifyListeners();
      return false;
    }

    _isProcessingPayment = true;
    _error = null;
    notifyListeners();

    try {
      switch (_selectedPaymentMethod!.type) {
        case PaymentType.digitalWallet:
          return await _processDigitalWalletPayment(
            orderId: orderId,
            amount: amount,
            customerInfo: customerInfo,
            paymentMethod: _selectedPaymentMethod!.id,
          );
        case PaymentType.card:
          return await _processCardPayment(
            orderId: orderId,
            amount: amount,
            customerInfo: customerInfo,
            cardData: additionalData,
          );
        case PaymentType.bankTransfer:
          return await _processBankTransfer(
            orderId: orderId,
            amount: amount,
            customerInfo: customerInfo,
          );
        case PaymentType.cashOnDelivery:
          return await _processCashOnDelivery(
            orderId: orderId,
            amount: amount,
          );
      }
    } catch (e) {
      _error = "Payment processing failed: $e";
      log("Error processing payment: $_error");
      return false;
    } finally {
      _isProcessingPayment = false;
      notifyListeners();
    }
  }

  // Process digital wallet payment (Khalti, eSewa)
  Future<bool> _processDigitalWalletPayment({
    required String orderId,
    required double amount,
    required Map<String, dynamic> customerInfo,
    required String paymentMethod,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/payment/digital-wallet",
        data: {
          'orderId': orderId,
          'amount': amount * 100, // Convert to paisa for Khalti
          'paymentMethod': paymentMethod,
          'customerInfo': customerInfo,
        },
      );

      if (response.statusCode == 200) {
        _lastPaymentResult = PaymentResult.fromJson(response.data);
        log("Digital wallet payment initiated successfully");
        return true;
      } else {
        _error = "Failed to initiate payment";
        return false;
      }
    } catch (e) {
      _error = "Digital wallet payment failed: $e";
      log("Error in digital wallet payment: $_error");
      return false;
    }
  }

  // Process card payment
  Future<bool> _processCardPayment({
    required String orderId,
    required double amount,
    required Map<String, dynamic> customerInfo,
    Map<String, dynamic>? cardData,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/payment/card",
        data: {
          'orderId': orderId,
          'amount': amount,
          'customerInfo': customerInfo,
          'cardData': cardData,
        },
      );

      if (response.statusCode == 200) {
        _lastPaymentResult = PaymentResult.fromJson(response.data);
        log("Card payment processed successfully");
        return true;
      } else {
        _error = "Card payment failed";
        return false;
      }
    } catch (e) {
      _error = "Card payment failed: $e";
      log("Error in card payment: $_error");
      return false;
    }
  }

  // Process bank transfer
  Future<bool> _processBankTransfer({
    required String orderId,
    required double amount,
    required Map<String, dynamic> customerInfo,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/payment/bank-transfer",
        data: {
          'orderId': orderId,
          'amount': amount,
          'customerInfo': customerInfo,
        },
      );

      if (response.statusCode == 200) {
        _lastPaymentResult = PaymentResult.fromJson(response.data);
        log("Bank transfer initiated successfully");
        return true;
      } else {
        _error = "Bank transfer failed";
        return false;
      }
    } catch (e) {
      _error = "Bank transfer failed: $e";
      log("Error in bank transfer: $_error");
      return false;
    }
  }

  // Process cash on delivery
  Future<bool> _processCashOnDelivery({
    required String orderId,
    required double amount,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/payment/cod",
        data: {
          'orderId': orderId,
          'amount': amount,
        },
      );

      if (response.statusCode == 200) {
        _lastPaymentResult = PaymentResult(
          success: true,
          transactionId: 'COD_$orderId',
          message: 'Cash on Delivery order confirmed',
          paymentMethod: 'cod',
        );
        log("Cash on Delivery confirmed successfully");
        return true;
      } else {
        _error = "Failed to confirm Cash on Delivery";
        return false;
      }
    } catch (e) {
      _error = "Cash on Delivery failed: $e";
      log("Error in COD: $_error");
      return false;
    }
  }

  // Verify payment status
  Future<bool> verifyPayment(String transactionId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get(
        "/payment/verify/$transactionId",
      );

      if (response.statusCode == 200) {
        final verified = response.data['verified'] ?? false;
        if (verified) {
          log("Payment verified successfully");
        } else {
          _error = "Payment verification failed";
        }
        return verified;
      } else {
        _error = "Failed to verify payment";
        return false;
      }
    } catch (e) {
      _error = "Payment verification failed: $e";
      log("Error verifying payment: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get payment history
  Future<void> getPaymentHistory() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/payment/history");

      if (response.data != null) {
        _paymentHistory = (response.data as List)
            .map((paymentJson) => PaymentHistory.fromJson(paymentJson))
            .toList();
        log("Payment history loaded: ${_paymentHistory.length} payments");
      }
    } catch (e) {
      _error = "Failed to load payment history: $e";
      log("Error loading payment history: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear last payment result
  void clearLastPaymentResult() {
    _lastPaymentResult = null;
    notifyListeners();
  }
}
