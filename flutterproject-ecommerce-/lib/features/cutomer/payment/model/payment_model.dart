enum PaymentType {
  digitalWallet,
  card,
  bankTransfer,
  cashOnDelivery,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

class PaymentMethod {
  final String id;
  final String name;
  final PaymentType type;
  final String icon;
  final bool isEnabled;
  final String description;
  final Map<String, dynamic>? additionalInfo;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.type,
    required this.icon,
    required this.isEnabled,
    required this.description,
    this.additionalInfo,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: PaymentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => PaymentType.card,
      ),
      icon: json['icon'] ?? '',
      isEnabled: json['isEnabled'] ?? true,
      description: json['description'] ?? '',
      additionalInfo: json['additionalInfo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.toString().split('.').last,
      'icon': icon,
      'isEnabled': isEnabled,
      'description': description,
      'additionalInfo': additionalInfo,
    };
  }
}

class PaymentResult {
  final bool success;
  final String? transactionId;
  final String message;
  final String? paymentMethod;
  final double? amount;
  final DateTime? timestamp;
  final Map<String, dynamic>? additionalData;

  PaymentResult({
    required this.success,
    this.transactionId,
    required this.message,
    this.paymentMethod,
    this.amount,
    this.timestamp,
    this.additionalData,
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) {
    return PaymentResult(
      success: json['success'] ?? false,
      transactionId: json['transactionId'],
      message: json['message'] ?? '',
      paymentMethod: json['paymentMethod'],
      amount: json['amount']?.toDouble(),
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp']) 
          : null,
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transactionId': transactionId,
      'message': message,
      'paymentMethod': paymentMethod,
      'amount': amount,
      'timestamp': timestamp?.toIso8601String(),
      'additionalData': additionalData,
    };
  }
}

class PaymentHistory {
  final String id;
  final String orderId;
  final double amount;
  final String paymentMethod;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? transactionId;
  final String? failureReason;

  PaymentHistory({
    required this.id,
    required this.orderId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.transactionId,
    this.failureReason,
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) {
    return PaymentHistory(
      id: json['_id'] ?? json['id'] ?? '',
      orderId: json['orderId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      paymentMethod: json['paymentMethod'] ?? '',
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      transactionId: json['transactionId'],
      failureReason: json['failureReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderId': orderId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'transactionId': transactionId,
      'failureReason': failureReason,
    };
  }

  String get statusText {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
    }
  }

  String get paymentMethodDisplayName {
    switch (paymentMethod.toLowerCase()) {
      case 'khalti':
        return 'Khalti';
      case 'esewa':
        return 'eSewa';
      case 'card':
        return 'Credit/Debit Card';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'cod':
        return 'Cash on Delivery';
      default:
        return paymentMethod;
    }
  }
}

class CardDetails {
  final String cardNumber;
  final String expiryMonth;
  final String expiryYear;
  final String cvv;
  final String cardHolderName;

  CardDetails({
    required this.cardNumber,
    required this.expiryMonth,
    required this.expiryYear,
    required this.cvv,
    required this.cardHolderName,
  });

  Map<String, dynamic> toJson() {
    return {
      'cardNumber': cardNumber,
      'expiryMonth': expiryMonth,
      'expiryYear': expiryYear,
      'cvv': cvv,
      'cardHolderName': cardHolderName,
    };
  }

  bool get isValid {
    return cardNumber.isNotEmpty &&
           expiryMonth.isNotEmpty &&
           expiryYear.isNotEmpty &&
           cvv.isNotEmpty &&
           cardHolderName.isNotEmpty &&
           cardNumber.length >= 13 &&
           cvv.length >= 3;
  }
}
