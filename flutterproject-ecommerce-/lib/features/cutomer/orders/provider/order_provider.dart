import 'dart:developer';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../model/order_model.dart';

class OrderProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  bool _isCreatingOrder = false;
  bool _isProcessingPayment = false;
  String? _error;
  List<OrderModel> _orders = [];
  OrderModel? _currentOrder;

  // Getters
  bool get isLoading => _isLoading;
  bool get isCreatingOrder => _isCreatingOrder;
  bool get isProcessingPayment => _isProcessingPayment;
  String? get error => _error;
  List<OrderModel> get orders => _orders;
  OrderModel? get currentOrder => _currentOrder;

  // Get user's orders
  Future<void> getUserOrders() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/orders/user");

      if (response.data != null) {
        _orders = (response.data as List)
            .map((orderJson) => OrderModel.fromJson(orderJson))
            .toList();
        log("Orders loaded: ${_orders.length} orders");
      }
    } catch (e) {
      _error = "Failed to load orders: $e";
      log("Error loading orders: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get order by ID
  Future<void> getOrderById(String orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/orders/$orderId");

      if (response.data != null) {
        _currentOrder = OrderModel.fromJson(response.data);
        log("Order loaded: ${_currentOrder?.orderNumber}");
      }
    } catch (e) {
      _error = "Failed to load order: $e";
      log("Error loading order: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create new order
  Future<bool> createOrder({
    required List<Map<String, dynamic>> orderItems,
    required ShippingAddress shippingAddress,
    required double totalPrice,
  }) async {
    _isCreatingOrder = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/orders",
        data: {
          'orderItem': orderItems,
          'shippingAddress': shippingAddress.toJson(),
          'totalPrice': totalPrice,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        _currentOrder = OrderModel.fromJson(response.data);
        log("Order created successfully: ${_currentOrder?.orderNumber}");

        // Refresh orders list
        await getUserOrders();
        return true;
      } else {
        _error = "Failed to create order";
        return false;
      }
    } catch (e) {
      _error = "Failed to create order: $e";
      log("Error creating order: $_error");
      return false;
    } finally {
      _isCreatingOrder = false;
      notifyListeners();
    }
  }

  // Process payment for order
  Future<bool> processOrderPayment({
    required String orderId,
    required String paymentMethod,
    required double amount,
  }) async {
    _isProcessingPayment = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/orders/$orderId/payment",
        data: {
          'method': paymentMethod,
          'amount': amount,
        },
      );

      if (response.statusCode == 200) {
        log("Payment processed successfully for order: $orderId");

        // Refresh current order and orders list
        await getOrderById(orderId);
        await getUserOrders();
        return true;
      } else {
        _error = "Failed to process payment";
        return false;
      }
    } catch (e) {
      _error = "Failed to process payment: $e";
      log("Error processing payment: $_error");
      return false;
    } finally {
      _isProcessingPayment = false;
      notifyListeners();
    }
  }

  // Confirm payment for order
  Future<bool> confirmOrderPayment({
    required String orderId,
    required String transactionId,
  }) async {
    _isProcessingPayment = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/orders/$orderId/payment/confirm",
        data: {
          'transactionId': transactionId,
        },
      );

      if (response.statusCode == 200) {
        log("Payment confirmed successfully for order: $orderId");

        // Refresh current order and orders list
        await getOrderById(orderId);
        await getUserOrders();
        return true;
      } else {
        _error = "Failed to confirm payment";
        return false;
      }
    } catch (e) {
      _error = "Failed to confirm payment: $e";
      log("Error confirming payment: $_error");
      return false;
    } finally {
      _isProcessingPayment = false;
      notifyListeners();
    }
  }

  // Get orders by status
  List<OrderModel> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get recent orders (last 5)
  List<OrderModel> get recentOrders {
    final sortedOrders = List<OrderModel>.from(_orders);
    sortedOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedOrders.take(5).toList();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear current order
  void clearCurrentOrder() {
    _currentOrder = null;
    notifyListeners();
  }

  // Initialize orders (call this when user logs in)
  Future<void> initializeOrders() async {
    await getUserOrders();
  }
}
