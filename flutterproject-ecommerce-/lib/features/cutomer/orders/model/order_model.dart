import '../../../cutomer/products/model/productmodel.dart';

class OrderItem {
  final String productId;
  final int quantity;
  final ProductModel? product; // Populated product details

  OrderItem({
    required this.productId,
    required this.quantity,
    this.product,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['productId'] is Map 
          ? json['productId']['_id'] 
          : json['productId'],
      quantity: json['quantity'] ?? 1,
      product: json['productId'] is Map 
          ? ProductModel.fromJson(json['productId'])
          : null,
    );
  }

  double get totalPrice {
    if (product != null) {
      return (product!.price * quantity).toDouble();
    }
    return 0.0;
  }
}

class ShippingAddress {
  final String city;
  final String province;
  final String country;
  final String? street;

  ShippingAddress({
    required this.city,
    required this.province,
    required this.country,
    this.street,
  });

  factory ShippingAddress.fromJson(Map<String, dynamic> json) {
    return ShippingAddress(
      city: json['city'] ?? '',
      province: json['province'] ?? '',
      country: json['country'] ?? 'Nepal',
      street: json['street'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'city': city,
      'province': province,
      'country': country,
      'street': street,
    };
  }
}

class OrderModel {
  final String id;
  final String orderNumber;
  final String userId;
  final List<OrderItem> orderItems;
  final String status;
  final double totalPrice;
  final ShippingAddress shippingAddress;
  final DateTime createdAt;
  final String? paymentId;

  OrderModel({
    required this.id,
    required this.orderNumber,
    required this.userId,
    required this.orderItems,
    required this.status,
    required this.totalPrice,
    required this.shippingAddress,
    required this.createdAt,
    this.paymentId,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['_id'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      userId: json['userId'] ?? '',
      orderItems: (json['orderItem'] as List<dynamic>?)
          ?.map((item) => OrderItem.fromJson(item))
          .toList() ?? [],
      status: json['status'] ?? 'PENDING',
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      shippingAddress: ShippingAddress.fromJson(json['shippingAddress'] ?? {}),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      paymentId: json['payment'],
    );
  }

  // Get status display text
  String get statusDisplay {
    switch (status) {
      case 'PENDING':
        return 'Pending';
      case 'CONFIRMED':
        return 'Confirmed';
      case 'SHIPPED':
        return 'Shipped';
      case 'DELIVERY':
        return 'Delivered';
      default:
        return status;
    }
  }

  // Get status color
  String get statusColor {
    switch (status) {
      case 'PENDING':
        return 'orange';
      case 'CONFIRMED':
        return 'blue';
      case 'SHIPPED':
        return 'purple';
      case 'DELIVERY':
        return 'green';
      default:
        return 'grey';
    }
  }

  // Check if order can be cancelled
  bool get canBeCancelled {
    return status == 'PENDING' || status == 'CONFIRMED';
  }

  // Calculate total items count
  int get totalItems {
    return orderItems.fold(0, (sum, item) => sum + item.quantity);
  }
}
