import 'dart:developer';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../../products/model/productmodel.dart';

class SearchProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  bool _isSearching = false;
  String? _error;
  List<ProductModel> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _searchSuggestions = [];

  // Filter properties
  String _searchQuery = '';
  List<String> _selectedBrands = [];
  List<String> _selectedCategories = [];
  double _minPrice = 0;
  double _maxPrice = 10000;
  int _minRating = 0;
  String _sortBy =
      'relevance'; // relevance, price_low, price_high, rating, newest

  // Pagination
  int _currentPage = 1;
  final int _totalPages = 1;
  bool _hasMoreResults = false;

  // Getters
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String? get error => _error;
  List<ProductModel> get searchResults => _searchResults;
  List<String> get searchHistory => _searchHistory;
  List<String> get searchSuggestions => _searchSuggestions;

  String get searchQuery => _searchQuery;
  List<String> get selectedBrands => _selectedBrands;
  List<String> get selectedCategories => _selectedCategories;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  int get minRating => _minRating;
  String get sortBy => _sortBy;

  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  bool get hasMoreResults => _hasMoreResults;
  bool get hasResults => _searchResults.isNotEmpty;

  // Search products
  Future<void> searchProducts({
    required String query,
    bool loadMore = false,
  }) async {
    if (!loadMore) {
      _isSearching = true;
      _currentPage = 1;
      _searchResults.clear();
    } else {
      _isLoading = true;
    }

    _error = null;
    _searchQuery = query;
    notifyListeners();

    try {
      final Map<String, dynamic> queryParams = {
        'search': query,
        'page': _currentPage,
        'limit': 20,
      };

      // Add filters to query
      if (_selectedBrands.isNotEmpty) {
        queryParams['brands'] = _selectedBrands;
      }
      if (_selectedCategories.isNotEmpty) {
        queryParams['category'] =
            _selectedCategories.first; // API might support only one category
      }
      if (_minPrice > 0) {
        queryParams['min'] = _minPrice;
      }
      if (_maxPrice < 10000) {
        queryParams['max'] = _maxPrice;
      }

      final response = await dioClient.dio.get(
        "/products",
        queryParameters: queryParams,
      );

      if (response.data != null) {
        final data = response.data['data'] as List;
        final newProducts = data.map((e) => ProductModel.fromJson(e)).toList();

        if (loadMore) {
          _searchResults.addAll(newProducts);
        } else {
          _searchResults = newProducts;
          // Add to search history if not already present
          if (query.isNotEmpty && !_searchHistory.contains(query)) {
            _searchHistory.insert(0, query);
            if (_searchHistory.length > 10) {
              _searchHistory = _searchHistory.take(10).toList();
            }
          }
        }

        // Update pagination info
        _hasMoreResults = newProducts.length ==
            20; // If we got full page, there might be more
        if (loadMore && newProducts.isNotEmpty) {
          _currentPage++;
        }

        log("Search completed: ${_searchResults.length} products found");
      }
    } catch (e) {
      _error = "Search failed: $e";
      log("Error searching products: $_error");
    } finally {
      _isSearching = false;
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load more results
  Future<void> loadMoreResults() async {
    if (!_hasMoreResults || _isLoading) return;

    _currentPage++;
    await searchProducts(query: _searchQuery, loadMore: true);
  }

  // Get search suggestions
  Future<void> getSearchSuggestions(String query) async {
    if (query.isEmpty) {
      _searchSuggestions.clear();
      notifyListeners();
      return;
    }

    try {
      // For now, filter from search history and add some common suggestions
      _searchSuggestions = _searchHistory
          .where((item) => item.toLowerCase().contains(query.toLowerCase()))
          .take(5)
          .toList();

      // Add some common search terms if not enough suggestions
      if (_searchSuggestions.length < 3) {
        final commonTerms = [
          'electronics',
          'clothing',
          'books',
          'home',
          'sports'
        ];
        for (final term in commonTerms) {
          if (term.toLowerCase().contains(query.toLowerCase()) &&
              !_searchSuggestions.contains(term)) {
            _searchSuggestions.add(term);
            if (_searchSuggestions.length >= 5) break;
          }
        }
      }

      notifyListeners();
    } catch (e) {
      log("Error getting search suggestions: $e");
    }
  }

  // Filter methods
  void updateBrandFilter(List<String> brands) {
    _selectedBrands = brands;
    notifyListeners();
  }

  void updateCategoryFilter(List<String> categories) {
    _selectedCategories = categories;
    notifyListeners();
  }

  void updatePriceFilter(double min, double max) {
    _minPrice = min;
    _maxPrice = max;
    notifyListeners();
  }

  void updateRatingFilter(int rating) {
    _minRating = rating;
    notifyListeners();
  }

  void updateSortBy(String sortBy) {
    _sortBy = sortBy;
    notifyListeners();
  }

  // Apply filters and re-search
  Future<void> applyFilters() async {
    if (_searchQuery.isNotEmpty) {
      await searchProducts(query: _searchQuery);
    }
  }

  // Clear filters
  void clearFilters() {
    _selectedBrands.clear();
    _selectedCategories.clear();
    _minPrice = 0;
    _maxPrice = 10000;
    _minRating = 0;
    _sortBy = 'relevance';
    notifyListeners();
  }

  // Clear search
  void clearSearch() {
    _searchQuery = '';
    _searchResults.clear();
    _searchSuggestions.clear();
    _error = null;
    _currentPage = 1;
    _hasMoreResults = false;
    notifyListeners();
  }

  // Remove from search history
  void removeFromHistory(String query) {
    _searchHistory.remove(query);
    notifyListeners();
  }

  // Clear search history
  void clearSearchHistory() {
    _searchHistory.clear();
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
