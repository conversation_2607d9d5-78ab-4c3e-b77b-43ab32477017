class UserAddress {
  final String id;
  final String street;
  final String city;
  final String province;
  final String country;
  final String? postalCode;
  final bool isDefault;

  UserAddress({
    required this.id,
    required this.street,
    required this.city,
    required this.province,
    required this.country,
    this.postalCode,
    this.isDefault = false,
  });

  factory UserAddress.fromJson(Map<String, dynamic> json) {
    return UserAddress(
      id: json['_id'] ?? '',
      street: json['street'] ?? '',
      city: json['city'] ?? '',
      province: json['province'] ?? '',
      country: json['country'] ?? '',
      postalCode: json['postalCode'],
      isDefault: json['isDefault'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'street': street,
      'city': city,
      'province': province,
      'country': country,
      'postalCode': postalCode,
      'isDefault': isDefault,
    };
  }

  // Get formatted address string
  String get formattedAddress {
    final parts = <String>[];
    if (street.isNotEmpty) parts.add(street);
    if (city.isNotEmpty) parts.add(city);
    if (province.isNotEmpty) parts.add(province);
    if (country.isNotEmpty) parts.add(country);
    if (postalCode != null && postalCode!.isNotEmpty) parts.add(postalCode!);
    return parts.join(', ');
  }

  UserAddress copyWith({
    String? id,
    String? street,
    String? city,
    String? province,
    String? country,
    String? postalCode,
    bool? isDefault,
  }) {
    return UserAddress(
      id: id ?? this.id,
      street: street ?? this.street,
      city: city ?? this.city,
      province: province ?? this.province,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      isDefault: isDefault ?? this.isDefault,
    );
  }
}

class UserProfileModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? profileImageUrl;
  final String role;
  final List<UserAddress> addresses;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfileModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.profileImageUrl,
    required this.role,
    required this.addresses,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      profileImageUrl: json['profileImageUrl'],
      role: json['role'] ?? 'USER',
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((address) => UserAddress.fromJson(address))
          .toList() ?? [],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profileImageUrl': profileImageUrl,
      'role': role,
      'addresses': addresses.map((address) => address.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Get default address
  UserAddress? get defaultAddress {
    try {
      return addresses.firstWhere((address) => address.isDefault);
    } catch (e) {
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  // Check if user is admin
  bool get isAdmin => role == 'ADMIN';

  // Check if user is merchant
  bool get isMerchant => role == 'MERCHANT';

  // Check if user is regular user
  bool get isUser => role == 'USER';

  // Get role display name
  String get roleDisplay {
    switch (role) {
      case 'ADMIN':
        return 'Administrator';
      case 'MERCHANT':
        return 'Merchant';
      case 'USER':
        return 'Customer';
      default:
        return role;
    }
  }

  // Get user initials for avatar
  String get initials {
    if (name.isEmpty) return 'U';
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }

  // Check if profile has image
  bool get hasProfileImage => profileImageUrl != null && profileImageUrl!.isNotEmpty;

  // Get formatted join date
  String get joinDate {
    return "${createdAt.day}/${createdAt.month}/${createdAt.year}";
  }

  // Check if profile is complete
  bool get isComplete {
    return name.isNotEmpty && 
           email.isNotEmpty && 
           phone != null && 
           phone!.isNotEmpty;
  }

  // Get completion percentage
  double get completionPercentage {
    int completed = 0;
    int total = 5; // name, email, phone, address, profile image
    
    if (name.isNotEmpty) completed++;
    if (email.isNotEmpty) completed++;
    if (phone != null && phone!.isNotEmpty) completed++;
    if (addresses.isNotEmpty) completed++;
    if (hasProfileImage) completed++;
    
    return completed / total;
  }

  UserProfileModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImageUrl,
    String? role,
    List<UserAddress>? addresses,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      addresses: addresses ?? this.addresses,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserProfileModel(id: $id, name: $name, email: $email, role: $role)';
  }
}
