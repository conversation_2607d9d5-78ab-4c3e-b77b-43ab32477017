import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/cutomer/profile/model/newusermodel.dart';

import 'package:flutter/widgets.dart';

class UpdateUserProfileImgProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  List<NewUserModel>? _newUserModel;

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<NewUserModel>? get newUserModel => _newUserModel;
  Future<void> updateUserProfile({
    required String userId,
    required String name,
    required String email,
    required String phone,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/users/$userId",
        data: {
          {"name": name, "email": email, "phone": phone},
        },
        options: Options(headers: {'Content-Type': 'application/json'}),
      );

      final data = response.data['user'] as List;
      _newUserModel = data.map((e) => NewUserModel.fromJson(e)).toList();
      log(_newUserModel.toString());
    } catch (e) {
      if (e is DioException) {
        _error = e.response?.data.toString() ?? e.message;
        log("Backend error: $_error");
      } else {
        _error = e.toString();
        log("Error: $_error");
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
