import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/cutomer/profile/model/newusermodel.dart';

import 'package:flutter/widgets.dart';

class UpdateUserProfileImgProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  List<NewUserModel>? _newUserModel;

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<NewUserModel>? get newUserModel => _newUserModel;
  Future<void> updateUserProfile({
    required String userId,
    required File profileImage,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Convert the File into MultipartFile
      String fileName = profileImage.path.split('/').last;
      FormData formData = FormData.fromMap({
        "profileImage": await MultipartFile.fromFile(
          profileImage.path,
          filename: fileName,
        ),
      });

      final response = await dioClient.dio.patch(
        "/users/$userId/profile-img",
        data: formData,
        options: Options(headers: {"Content-Type": "multipart/form-data"}),
      );

      final data = response.data['user'];

      log(data.toString());
    } catch (e) {
      if (e is DioException) {
        _error = e.response?.data.toString() ?? e.message;
        log("Backend error: $_error");
      } else {
        _error = e.toString();
        log("Error: $_error");
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
