import 'dart:developer';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/network/dioclient.dart';
import '../model/user_profile_model.dart';

class UserProfileProvider with ChangeNotifier {
  final DioClient dioClient = DioClient();

  bool _isLoading = false;
  bool _isUpdating = false;
  bool _isUploadingImage = false;
  String? _error;
  UserProfileModel? _userProfile;

  // Getters
  bool get isLoading => _isLoading;
  bool get isUpdating => _isUpdating;
  bool get isUploadingImage => _isUploadingImage;
  String? get error => _error;
  UserProfileModel? get userProfile => _userProfile;

  // Get user profile
  Future<void> getUserProfile() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.get("/users/profile");

      if (response.data != null) {
        _userProfile = UserProfileModel.fromJson(response.data);
        log("User profile loaded: ${_userProfile?.name}");
      }
    } catch (e) {
      _error = "Failed to load profile: $e";
      log("Error loading profile: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update user profile
  Future<bool> updateUserProfile({
    String? name,
    String? email,
    String? phone,
    String? address,
  }) async {
    _isUpdating = true;
    _error = null;
    notifyListeners();

    try {
      final Map<String, dynamic> updateData = {};

      if (name != null) updateData['name'] = name;
      if (email != null) updateData['email'] = email;
      if (phone != null) updateData['phone'] = phone;
      if (address != null) updateData['address'] = address;

      final response = await dioClient.dio.put(
        "/users/profile",
        data: updateData,
      );

      if (response.statusCode == 200) {
        _userProfile = UserProfileModel.fromJson(response.data);
        log("Profile updated successfully");
        return true;
      } else {
        _error = "Failed to update profile";
        return false;
      }
    } catch (e) {
      _error = "Failed to update profile: $e";
      log("Error updating profile: $_error");
      return false;
    } finally {
      _isUpdating = false;
      notifyListeners();
    }
  }

  // Upload profile image
  Future<bool> uploadProfileImage(File imageFile) async {
    _isUploadingImage = true;
    _error = null;
    notifyListeners();

    try {
      // Create form data for file upload
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'profile_image.jpg',
        ),
      });

      final response = await dioClient.dio.post(
        "/users/profile/image",
        data: formData,
      );

      if (response.statusCode == 200) {
        // Refresh user profile to get updated image URL
        await getUserProfile();
        log("Profile image uploaded successfully");
        return true;
      } else {
        _error = "Failed to upload image";
        return false;
      }
    } catch (e) {
      _error = "Failed to upload image: $e";
      log("Error uploading image: $_error");
      return false;
    } finally {
      _isUploadingImage = false;
      notifyListeners();
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _isUpdating = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/users/change-password",
        data: {'currentPassword': currentPassword, 'newPassword': newPassword},
      );

      if (response.statusCode == 200) {
        log("Password changed successfully");
        return true;
      } else {
        _error = "Failed to change password";
        return false;
      }
    } catch (e) {
      _error = "Failed to change password: $e";
      log("Error changing password: $_error");
      return false;
    } finally {
      _isUpdating = false;
      notifyListeners();
    }
  }

  // Add address
  Future<bool> addAddress({
    required String street,
    required String city,
    required String province,
    required String country,
    String? postalCode,
  }) async {
    _isUpdating = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/users/addresses",
        data: {
          'street': street,
          'city': city,
          'province': province,
          'country': country,
          'postalCode': postalCode,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Refresh user profile to get updated addresses
        await getUserProfile();
        log("Address added successfully");
        return true;
      } else {
        _error = "Failed to add address";
        return false;
      }
    } catch (e) {
      _error = "Failed to add address: $e";
      log("Error adding address: $_error");
      return false;
    } finally {
      _isUpdating = false;
      notifyListeners();
    }
  }

  // Update address
  Future<bool> updateAddress({
    required String addressId,
    required String street,
    required String city,
    required String province,
    required String country,
    String? postalCode,
  }) async {
    _isUpdating = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.put(
        "/users/addresses/$addressId",
        data: {
          'street': street,
          'city': city,
          'province': province,
          'country': country,
          'postalCode': postalCode,
        },
      );

      if (response.statusCode == 200) {
        // Refresh user profile to get updated addresses
        await getUserProfile();
        log("Address updated successfully");
        return true;
      } else {
        _error = "Failed to update address";
        return false;
      }
    } catch (e) {
      _error = "Failed to update address: $e";
      log("Error updating address: $_error");
      return false;
    } finally {
      _isUpdating = false;
      notifyListeners();
    }
  }

  // Delete address
  Future<bool> deleteAddress(String addressId) async {
    _isUpdating = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.delete(
        "/users/addresses/$addressId",
      );

      if (response.statusCode == 200) {
        // Refresh user profile to get updated addresses
        await getUserProfile();
        log("Address deleted successfully");
        return true;
      } else {
        _error = "Failed to delete address";
        return false;
      }
    } catch (e) {
      _error = "Failed to delete address: $e";
      log("Error deleting address: $_error");
      return false;
    } finally {
      _isUpdating = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Initialize profile (call this when user logs in)
  Future<void> initializeProfile() async {
    await getUserProfile();
  }

  // Clear profile data (call this when user logs out)
  void clearProfile() {
    _userProfile = null;
    _error = null;
    notifyListeners();
  }

  // Check if profile is complete
  bool get isProfileComplete {
    if (_userProfile == null) return false;
    return _userProfile!.name.isNotEmpty &&
        _userProfile!.email.isNotEmpty &&
        _userProfile!.phone?.isNotEmpty == true;
  }

  // Get user initials for avatar
  String get userInitials {
    if (_userProfile?.name == null || _userProfile!.name.isEmpty) {
      return 'U';
    }
    final names = _userProfile!.name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return _userProfile!.name[0].toUpperCase();
  }
}
