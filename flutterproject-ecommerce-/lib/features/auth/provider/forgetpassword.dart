import 'dart:developer';

import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/auth/model/usermodel.dart';
import 'package:flutter/material.dart';

class ForgetPasswordProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  UserModel? _userModel;

  String? _token;
  String? _userId;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get userId => _userId;
  String? get token => _token;
  UserModel? get userModel => _userModel;

  Future<void> forgetPassword({required String email}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/auth/forget-Password",
        data: {'email': email},
      );

      _token = response.data['user']['data']['token'];
      _userId = response.data['user']['data']['userId'];
    } catch (e) {
      _error = e.toString();
      log(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
