import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/core/services/auth_service.dart';
import 'package:ecom/features/auth/model/usermodel.dart';
import 'package:flutter/material.dart';

class LoginProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;
  final AuthService authService = AuthService.instance;

  bool _isLoading = false;
  bool _isInitializing = false;
  bool _isLoggedIn = false;
  String? _error;
  UserModel? _userModel;

  bool get isLoading => _isLoading;
  bool get isInitializing => _isInitializing;
  bool get isLoggedIn => _isLoggedIn;
  String? get error => _error;
  UserModel? get userModel => _userModel;

  /// Initialize authentication state on app startup
  Future<void> initializeAuth() async {
    _isInitializing = true;
    _error = null;
    notifyListeners();

    try {
      // Initialize AuthService
      await authService.init();

      // Check if user is already logged in
      if (authService.isLoggedIn()) {
        _userModel = authService.getUserData();
        _isLoggedIn = true;
        log('LoginProvider: User already logged in');
      } else {
        log('LoginProvider: No existing login found');
      }
    } catch (e) {
      _error = 'Failed to initialize authentication: $e';
      log('LoginProvider: Initialization error: $e');
    } finally {
      _isInitializing = false;
      notifyListeners();
    }
  }

  /// Clear error state
  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<bool> login({required String email, required String password}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/auth/login",
        data: {'email': email, 'password': password},
        options: Options(headers: {'Content-Type': 'application/json'}),
      );

      final token = response.data['authToken'];
      final userData = response.data['user'];

      if (token == null || userData == null) {
        throw Exception('Invalid response: missing token or user data');
      }

      // Create user model
      _userModel = UserModel.fromJson(userData);

      // Save authentication data to SharedPreferences
      final authSaved = await authService.saveAuthData(
        token: token,
        user: _userModel!,
      );

      if (!authSaved) {
        throw Exception('Failed to save authentication data');
      }

      _isLoggedIn = true;
      log('LoginProvider: Login successful for user: ${_userModel!.email}');
      log('LoginProvider: Token saved: ${token.substring(0, 20)}...');

      return true;
    } on DioException catch (e) {
      _error = e.response?.data['message'] ?? e.message;
      log("LoginProvider: DioException: $_error");
      return false;
    } catch (e) {
      _error = e.toString();
      log("LoginProvider: Login error: $_error");
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Logout user and clear all authentication data
  Future<bool> logout() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Clear authentication data from SharedPreferences
      final authCleared = await authService.clearAuthData();

      if (!authCleared) {
        throw Exception('Failed to clear authentication data');
      }

      // Clear cookies from DioClient
      dioClient.clearCookies();

      // Reset local state
      _userModel = null;
      _isLoggedIn = false;

      log('LoginProvider: Logout successful');
      return true;
    } catch (e) {
      _error = 'Logout failed: $e';
      log('LoginProvider: Logout error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Validate current authentication state
  Future<bool> validateAuth() async {
    try {
      if (!authService.isLoggedIn()) {
        log('LoginProvider: No valid authentication found');
        return false;
      }

      // Optional: Make a test API call to validate token with backend
      // This could be a call to /auth/validate or similar endpoint

      return true;
    } catch (e) {
      log('LoginProvider: Auth validation error: $e');
      return false;
    }
  }

  /// Check if user has valid authentication
  bool hasValidAuth() {
    return _isLoggedIn &&
        _userModel != null &&
        authService.hasCompleteAuthData();
  }

  /// Get authentication summary for debugging
  Map<String, dynamic> getAuthSummary() {
    return {
      'providerLoggedIn': _isLoggedIn,
      'hasUserModel': _userModel != null,
      'userEmail': _userModel?.email,
      ...authService.getAuthSummary(),
    };
  }
}
