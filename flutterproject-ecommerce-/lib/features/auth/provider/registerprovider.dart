import 'dart:developer';

import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/auth/model/usermodel.dart';
import 'package:flutter/material.dart';

class RegisterProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  UserModel? _userModel;

  bool get isLoading => _isLoading;
  String? get error => _error;
  UserModel? get userModel => _userModel;

  Future<void> register({
    required String name,
    required String email,
    required String password,
    required String phone,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await dioClient.dio.post(
        "/auth/register",
        data: {
          'name': name,
          'email': email,
          'phone': phone,
          'password': password,
        },
      );

      _userModel = UserModel.fromJson(response.data['user']);
      log(_userModel.toString());
    } catch (e) {
      _error = e.toString();
      log(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
