import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/auth/model/usermodel.dart';
import 'package:flutter/material.dart';

class ResetPasswordProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  UserModel? _userModel;

  String? _token;
  String? _userId;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get userId => _userId;
  String? get token => _token;
  UserModel? get userModel => _userModel;

  Future<void> resetPassword({
    required String password,
    required String userId,
    required String token,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    log(userId.toString());
    log(token.toString());
    log(password.toString());
    try {
      await dioClient.dio.post(
        "/auth/set-password?token=$token&userId=$userId",
        data: {'password': password},
      );

      // _token = response.data['user']['data']['token'];
      // _userId = response.data['user']['data']['userId'];

      // log(_token.toString());
      // log(_userId.toString());
    } catch (e) {
      if (e is DioException) {
        // Backend response from the server
        _error = e.response?.data.toString() ?? e.message;
        log("Backend error: $_error");
      } else {
        // Other exceptions
        _error = e.toString();
        log("Error: $_error");
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
