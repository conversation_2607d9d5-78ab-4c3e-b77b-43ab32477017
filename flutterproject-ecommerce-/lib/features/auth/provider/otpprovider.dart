import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:ecom/core/network/dioclient.dart';
import 'package:ecom/features/auth/model/usermodel.dart';
import 'package:flutter/material.dart';

class OtpProvider with ChangeNotifier {
  final DioClient dioClient = DioClient.instance;

  bool _isLoading = false;
  String? _error;
  UserModel? _userModel;

  bool get isLoading => _isLoading;
  String? get error => _error;
  UserModel? get userModel => _userModel;

  Future<void> otp({required String userId, required String token}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await dioClient.dio.post("/auth/verify-otp?token=$token&userId=$userId");
    } catch (e) {
      if (e is DioException) {
        // Backend response from the server
        _error = e.response?.data.toString() ?? e.message;
        log("Backend error: $_error");
      } else {
        // Other exceptions
        _error = e.toString();
        log("Error: $_error");
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
