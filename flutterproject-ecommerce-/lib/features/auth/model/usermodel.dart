class UserModel {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String? password;
  final List<String> roles;
  final String? token;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    this.token,
    this.password,
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.roles,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'],
      token: json['token'],
      name: json['name'],
      email: json['email'],
      password: json['password'] != null ? json['password'] as String : null,
      phone: json['phone'],
      roles: List<String>.from(json['roles']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'token': token,
      'name': name,
      'email': email,
      'password': password,
      'phone': phone,
      'roles': roles,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
