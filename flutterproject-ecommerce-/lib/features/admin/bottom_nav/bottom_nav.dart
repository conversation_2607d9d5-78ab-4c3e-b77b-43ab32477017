import 'package:ecom/features/admin/home/<USER>/admin_dashboard.dart';
import 'package:ecom/features/cutomer/cart/pages/cart_add.dart';
import 'package:ecom/features/cutomer/category/pages/category.dart';

import 'package:ecom/features/cutomer/profile/pages/profileimage.dart';
import 'package:flutter/material.dart';

class AdminButtonNavPage extends StatefulWidget {
  const AdminButtonNavPage({super.key});

  @override
  State<AdminButtonNavPage> createState() => _ButtonNavPageState();
}

class _ButtonNavPageState extends State<AdminButtonNavPage> {
  int currentIndex = 0;
  List screen = [DashboardPage(), CartPage(), CategoryPage(), ProfilePage()];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: screen[currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (value) => setState(() {
          currentIndex = value;
        }),
        selectedItemColor: Colors.green,
        unselectedItemColor: const Color.fromARGB(255, 102, 124, 109),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: "DashBoard",
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag_outlined),
            label: "Products",
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_basket_outlined),
            label: "Orders",
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: "Profile"),
        ],
      ),
    );
  }
}
