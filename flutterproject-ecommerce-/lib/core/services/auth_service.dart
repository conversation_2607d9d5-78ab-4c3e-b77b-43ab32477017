import 'dart:convert';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';
import '../../features/auth/model/usermodel.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';

  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();

  SharedPreferences? _prefs;

  /// Initialize SharedPreferences
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      log('AuthService: SharedPreferences initialized');
    } catch (e) {
      log('AuthService: Failed to initialize SharedPreferences: $e');
      rethrow;
    }
  }

  /// Get SharedPreferences instance
  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('AuthService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  /// Save authentication token
  Future<bool> saveToken(String token) async {
    try {
      final success = await prefs.setString(_tokenKey, token);
      if (success) {
        log('AuthService: Token saved successfully');
      } else {
        log('AuthService: Failed to save token');
      }
      return success;
    } catch (e) {
      log('AuthService: Error saving token: $e');
      return false;
    }
  }

  /// Get authentication token
  String? getToken() {
    try {
      final token = prefs.getString(_tokenKey);
      if (token != null) {
        log('AuthService: Token retrieved successfully');
      } else {
        log('AuthService: No token found');
      }
      return token;
    } catch (e) {
      log('AuthService: Error retrieving token: $e');
      return null;
    }
  }

  /// Save user data
  Future<bool> saveUserData(UserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      final success = await prefs.setString(_userDataKey, userJson);
      if (success) {
        log('AuthService: User data saved successfully');
      } else {
        log('AuthService: Failed to save user data');
      }
      return success;
    } catch (e) {
      log('AuthService: Error saving user data: $e');
      return false;
    }
  }

  /// Get user data
  UserModel? getUserData() {
    try {
      final userJson = prefs.getString(_userDataKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        final user = UserModel.fromJson(userMap);
        log('AuthService: User data retrieved successfully');
        return user;
      } else {
        log('AuthService: No user data found');
        return null;
      }
    } catch (e) {
      log('AuthService: Error retrieving user data: $e');
      return null;
    }
  }

  /// Set login status
  Future<bool> setLoggedIn(bool isLoggedIn) async {
    try {
      final success = await prefs.setBool(_isLoggedInKey, isLoggedIn);
      if (success) {
        log('AuthService: Login status set to $isLoggedIn');
      } else {
        log('AuthService: Failed to set login status');
      }
      return success;
    } catch (e) {
      log('AuthService: Error setting login status: $e');
      return false;
    }
  }

  /// Check if user is logged in
  bool isLoggedIn() {
    try {
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      final hasToken = getToken() != null;
      final hasUserData = getUserData() != null;
      
      final actuallyLoggedIn = isLoggedIn && hasToken && hasUserData;
      log('AuthService: User logged in status: $actuallyLoggedIn');
      return actuallyLoggedIn;
    } catch (e) {
      log('AuthService: Error checking login status: $e');
      return false;
    }
  }

  /// Save complete authentication data
  Future<bool> saveAuthData({
    required String token,
    required UserModel user,
  }) async {
    try {
      final tokenSaved = await saveToken(token);
      final userSaved = await saveUserData(user);
      final statusSet = await setLoggedIn(true);
      
      final success = tokenSaved && userSaved && statusSet;
      if (success) {
        log('AuthService: Complete auth data saved successfully');
      } else {
        log('AuthService: Failed to save complete auth data');
      }
      return success;
    } catch (e) {
      log('AuthService: Error saving auth data: $e');
      return false;
    }
  }

  /// Clear all authentication data (logout)
  Future<bool> clearAuthData() async {
    try {
      final tokenCleared = await prefs.remove(_tokenKey);
      final userCleared = await prefs.remove(_userDataKey);
      final statusCleared = await prefs.remove(_isLoggedInKey);
      
      final success = tokenCleared && userCleared && statusCleared;
      if (success) {
        log('AuthService: All auth data cleared successfully');
      } else {
        log('AuthService: Failed to clear some auth data');
      }
      return success;
    } catch (e) {
      log('AuthService: Error clearing auth data: $e');
      return false;
    }
  }

  /// Validate token format (basic validation)
  bool isValidTokenFormat(String? token) {
    if (token == null || token.isEmpty) {
      return false;
    }
    
    // Basic JWT format validation (header.payload.signature)
    final parts = token.split('.');
    return parts.length == 3;
  }

  /// Get formatted cookie string for backend
  String? getAuthCookie() {
    final token = getToken();
    if (token != null && isValidTokenFormat(token)) {
      // Format as cookie string that backend expects: "tokenName=tokenValue"
      return 'token=$token';
    }
    return null;
  }

  /// Check if authentication data is complete
  bool hasCompleteAuthData() {
    return getToken() != null && 
           getUserData() != null && 
           isLoggedIn();
  }

  /// Get authentication summary for debugging
  Map<String, dynamic> getAuthSummary() {
    return {
      'hasToken': getToken() != null,
      'hasUserData': getUserData() != null,
      'isLoggedIn': isLoggedIn(),
      'hasCompleteAuth': hasCompleteAuthData(),
      'tokenFormat': isValidTokenFormat(getToken()) ? 'valid' : 'invalid',
    };
  }
}
