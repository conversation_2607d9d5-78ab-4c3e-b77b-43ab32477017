import 'package:flutter/material.dart';
import '../../features/cutomer/search/pages/search_results_page.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  final TextEditingController searchController;
  final VoidCallback onMenuPressed;
  final VoidCallback onNotificationPressed;
  final VoidCallback onFilterPressed;

  const CustomAppbar({
    super.key,
    required this.searchController,
    required this.onMenuPressed,
    required this.onNotificationPressed,
    required this.onFilterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          // vertical: 10,
        ), // reduced padding
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top Row
            SizedBox(
              height: 40, // reduced height
              child: Row(
                children: [
                  _buildIconButton(icon: Icons.menu, onPressed: onMenuPressed),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Center(
                      child: const Text(
                        '9:41',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildIconButton(
                    icon: Icons.notifications_outlined,
                    onPressed: onNotificationPressed,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Search Bar
            Container(
              height: 50, // reduced height
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: 'Search keywords...',
                  hintStyle: TextStyle(color: Colors.grey[500], fontSize: 16),
                  prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                  suffixIcon: GestureDetector(
                    onTap: onFilterPressed,
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4CAF50),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.tune,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onSubmitted: (query) {
                  if (query.isNotEmpty) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            SearchResultsPage(initialQuery: query),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 20, color: Colors.grey[700]),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(114);
}
