import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:ecom/core/constants/api.dart';
import '../services/auth_service.dart';

class DioClient {
  static DioClient? _instance;
  static DioClient get instance => _instance ??= DioClient._();

  late Dio _dio;
  late CookieJar _cookieJar;

  DioClient._() {
    _initializeDio();
  }

  // Getter for backward compatibility
  Dio get dio => _dio;

  void _initializeDio() {
    // Initialize cookie jar for persistent cookies
    _cookieJar = CookieJar();

    // Create Dio instance with base configuration
    _dio = Dio(BaseOptions(
      baseUrl: BASEURI,
      headers: {"Content-Type": "application/json"},
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
    ));

    // Add cookie manager
    _dio.interceptors.add(<PERSON>ie<PERSON>anager(_cookieJar));

    // Add request interceptor for authentication
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          _addAuthCookie(options);
          log('DioClient: Request to ${options.path}');
          log('DioClient: Headers: ${options.headers}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          log('DioClient: Response ${response.statusCode} from ${response.requestOptions.path}');
          handler.next(response);
        },
        onError: (error, handler) {
          _handleAuthError(error);
          log('DioClient: Error ${error.response?.statusCode} from ${error.requestOptions.path}');
          log('DioClient: Error message: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );
  }

  /// Add authentication cookie to request
  void _addAuthCookie(RequestOptions options) {
    try {
      final authCookie = AuthService.instance.getAuthCookie();
      if (authCookie != null) {
        // Add cookie to headers in the format backend expects
        options.headers['Cookie'] = authCookie;
        log('DioClient: Added auth cookie: $authCookie');
      } else {
        log('DioClient: No auth cookie available');
      }
    } catch (e) {
      log('DioClient: Error adding auth cookie: $e');
    }
  }

  /// Handle authentication errors
  void _handleAuthError(DioException error) {
    if (error.response?.statusCode == 401) {
      log('DioClient: 401 Unauthorized - Token missing or expired');
      _handleTokenExpired();
    } else if (error.response?.statusCode == 400) {
      final message = error.response?.data?['message'] ?? '';
      if (message.toLowerCase().contains('token') ||
          message.toLowerCase().contains('auth')) {
        log('DioClient: 400 Bad Request - Invalid token');
        _handleInvalidToken();
      }
    }
  }

  /// Handle expired token
  void _handleTokenExpired() {
    try {
      // Clear stored auth data
      AuthService.instance.clearAuthData();
      log('DioClient: Cleared auth data due to token expiration');

      // Note: Navigation to login should be handled by the UI layer
      // through listening to authentication state changes
    } catch (e) {
      log('DioClient: Error handling token expiration: $e');
    }
  }

  /// Handle invalid token
  void _handleInvalidToken() {
    try {
      // Clear stored auth data
      AuthService.instance.clearAuthData();
      log('DioClient: Cleared auth data due to invalid token');
    } catch (e) {
      log('DioClient: Error handling invalid token: $e');
    }
  }

  /// Clear all cookies (useful for logout)
  void clearCookies() {
    try {
      _cookieJar.deleteAll();
      log('DioClient: All cookies cleared');
    } catch (e) {
      log('DioClient: Error clearing cookies: $e');
    }
  }

  /// Reinitialize Dio (useful after auth state changes)
  void reinitialize() {
    try {
      _initializeDio();
      log('DioClient: Reinitialized successfully');
    } catch (e) {
      log('DioClient: Error reinitializing: $e');
    }
  }
}
